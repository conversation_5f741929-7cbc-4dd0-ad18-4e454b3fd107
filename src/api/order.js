import request from '@/libs/http'

/**
 * @typedef {object} TreeDetail
 * @property {string} name
 * @property {string} description
 * @property {string[]} imageUrls
 * @property {number} stock
 */

/**
 * @typedef {object} Order
 * @property {number} id
 * @property {string} orderNo
 * @property {string} status
 * @property {number} totalAmount
 * @property {TreeDetail} treeDetail
 */

/**
 * @typedef {object} OrderListResponse
 * @property {Order[]} rows
 * @property {number} total
 * @property {number} page
 * @property {number} size
 */

/**
 * 查询订单列表
 * @param {Object} params - 查询参数
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {number} [params.status] - 订单状态
 * @returns {Promise<OrderListResponse>}
 */
export function getOrderList(params) {
  return request({
    url: '/app/orders/my-list',
    method: 'GET',
    params
  })
}

/**
 * @typedef {object} CreateOrderResponse
 * @property {string} orderId
 */

/**
 * 新增认养果树（创建订单）
 * @param {Object} data - 订单信息
 * @param {string} data.productId
 * @param {number} data.quantity
 * @param {string} data.addressId
 * @param {string} data.plaqueName
 * @param {string} data.message
 * @returns {Promise<CreateOrderResponse>}
 */
export function createOrder(data) {
  return request({
    url: '/orders',
    method: 'POST',
    data
  })
}

/**
 * 查询单个订单详情
 * @param {number} id - 订单ID
 * @returns {Promise<Order>}
 */
export function getOrderInfo(id) {
  return request({
    url: `/orders/${id}`,
    method: 'GET'
  })
}

/**
 * 修改订单
 * @param {number} id - 订单ID
 * @param {Object} data - 订单信息
 * @returns {Promise<Order>}
 */
export function updateOrder(id, data) {
  return request({
    url: `/orders/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 获取支付参数
 * @param {object} data
 * @param {string} data.orderId
 * @returns {Promise<any>}
 */
export function getPaymentParams(data) {
  return request({
    url: '/orders/payments',
    method: 'POST',
    data
  })
}

/**
 * @typedef {object} AdoptionItem
 * @property {string} id
 * @property {string} title
 * @property {string} subtitle
 * @property {string} imageUrl
 * @property {string} purchaseDate
 * @property {string} expectedHarvestDate
 * @property {object} progress
 * @property {number} progress.percentage
 * @property {string} progress.text
 * @property {string} progress.secondaryText
 * @property {string} certificateId
 * @property {object} benefitsPackage
 * @property {string} benefitsPackage.orderNumber
 * @property {string} benefitsPackage.title
 */

/**
 * @typedef {object} AdoptionListResponse
 * @property {AdoptionItem[]} rows
 * @property {number} total
 */

/**
 * 获取认养列表
 * @param {object} params
 * @param {string} [params.pageNum]
 * @param {string} [params.pageSize]
 * @param {string} [params.status]
 * @returns {Promise<AdoptionListResponse>}
 */
export function getAdoptionList(params) {
  return request({
    url: '/adoptions',
    method: 'GET',
    params
  })
}

/**
 * 删除订单
 * @param {number} id - 订单ID
 * @returns {Promise<void>}
 */
export function deleteOrder(id) {
  return request({
    url: `/orders/${id}`,
    method: 'DELETE'
  })
}

/**
 * @typedef {object} SubmitOrderResponse
 * @property {string} data - 订单号
 */

/**
 * 提交订单（新下单接口）
 * @param {Object} data - 订单信息
 * @param {number} data.addressId - 地址ID
 * @param {number} data.fruitTreeId - 果树ID
 * @param {number} data.fruitNum - 下单数量
 * @param {number} data.freightPrice - 运费
 * @param {string} data.labelName - 挂牌命名
 * @param {string} data.wishMsg - 寄语
 * @returns {Promise<SubmitOrderResponse>}
 */
export function submitOrder(data) {
  return request({
    url: '/app/orders/submit',
    method: 'POST',
    data
  })
}

/**
 * 获取支付参数（新支付接口）
 * @param {string} orderSn - 订单号
 * @returns {Promise<any>}
 */
export function getCashierPayParams(orderSn) {
  return request({
    url: '/app/payment/cashier/pay',
    method: 'GET',
    params: { orderSn }
  })
}
