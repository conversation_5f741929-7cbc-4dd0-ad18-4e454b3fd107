<template>
  <view class="detail-container">
    <!-- 轮播图组件 -->
    <DetailSwiper :images="treeDetail.imageUrls || []" />

    <!-- 产品信息组件 -->
    <ProductInfo
      :name="treeDetail.name"
      :description="treeDetail.description"
      :price="treeDetail.price"
      :salesCount="treeDetail.salesCount"
    />

    <!-- 产品权益组件 -->
    <ProductBenefits :benefits="treeDetail.benefits" />

    <!-- 商品详情组件 -->
    <ProductDetails :details="treeDetail.details" />

    <!-- 底部操作栏组件 -->
    <BottomActions
      ref="bottomActionsRef"
      :productInfo="treeDetail"
      @contact="handleContact"
      @pay="handlePay"
    />
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getFruitTreeDetail } from '@/api/tree'
import { submitOrder, getCashierPayParams } from '@/api/order'
import DetailSwiper from './components/DetailSwiper.vue'
import ProductInfo from './components/ProductInfo.vue'
import ProductBenefits from './components/ProductBenefits.vue'
import ProductDetails from './components/ProductDetails.vue'
import BottomActions from './components/BottomActions.vue'

// 响应式数据
const treeId = ref('')
const treeDetail = ref({
  imageUrls: []
})
const bottomActionsRef = ref(null)

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    treeId.value = options.id
    loadTreeDetail()
  }
})

// 加载果树详情数据
const loadTreeDetail = async () => {
  try {
    const res = await getFruitTreeDetail(treeId.value)
    treeDetail.value = res.data
    console.log(treeDetail.value, 'treeDetail')
  } catch (error) {
    console.error('加载果树详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 页面初始化
onMounted(() => {
  console.log('果树详情页面已加载')
})

// 事件处理方法
const handleContact = () => {
  uni.showToast({
    title: '联系客服功能待开发',
    icon: 'none'
  })
}

const handlePay = async (paymentData) => {
  try {
    // 1. 提交订单
    const orderRes = await submitOrder({
      addressId: paymentData.addressId,
      fruitTreeId: treeDetail.value.id,
      fruitNum: paymentData.quantity,
      freightPrice: 0.0, // 运费暂设为0，如需计算可后续添加逻辑
      labelName: paymentData.treeName,
      wishMsg: paymentData.message
    })

    if (orderRes.data) {
      // 2. 获取支付参数
      const paymentParams = await getCashierPayParams(orderRes.data)

      // 3. 拉起支付
      await uni.requestPayment({
        ...paymentParams.data,
        success: () => {
          // 支付成功：关闭弹窗并重置状态
          if (bottomActionsRef.value) {
            bottomActionsRef.value.closeModalAndReset()
          }
          uni.showToast({
            title: '支付成功',
            icon: 'success'
          })
          // TODO: 跳转到订单详情页或我的认养页
        },
        fail: (err) => {
          // 支付失败：只重置loading状态，保持弹窗打开让用户重试
          if (bottomActionsRef.value) {
            bottomActionsRef.value.resetPaymentLoading()
          }
          console.error('支付失败:', err)
          uni.showToast({
            title: '支付失败',
            icon: 'none'
          })
        }
      })
    } else {
      throw new Error('创建订单失败')
    }
  } catch (error) {
    // 异常情况：只重置loading状态，保持弹窗打开让用户重试
    if (bottomActionsRef.value) {
      bottomActionsRef.value.resetPaymentLoading()
    }
    console.error('支付流程出错:', error)
    uni.showToast({
      title: error.message || '支付失败',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-gray: #666666;
$text-dark: #1a1a1a;
$bg-gray: #f5f5f5;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 页面容器
.detail-container {
  min-height: 100vh;
  background-color: $bg-gray;
  display: flex;
  flex-direction: column;
}
</style>
